# Dependencies
node_modules/
python/venv/
__pycache__/
*.pyc
*.pyo
*.pyd

# Build outputs
dist/
dist-electron/
out/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Downloads
downloads/

# Temporary files
tmp/
temp/

# Electron
app/
release/

# SQLite databases
*.db
*.sqlite
*.sqlite3

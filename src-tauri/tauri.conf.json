{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "Spotify-Soulseek App", "version": "0.1.0", "identifier": "com.odmustafa.spotify-soulseek", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "Spotify-Soulseek App", "width": 1200, "height": 800, "resizable": true, "fullscreen": false, "center": true, "minWidth": 800, "minHeight": 600}], "security": {"csp": null}, "macOSPrivateApi": true}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}
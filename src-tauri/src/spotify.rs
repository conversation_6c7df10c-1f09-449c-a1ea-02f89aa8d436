use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use tauri::State;

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyTokens {
    access_token: String,
    refresh_token: String,
    expires_in: u64,
    token_type: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyUser {
    id: String,
    display_name: Option<String>,
    email: Option<String>,
    images: Vec<SpotifyImage>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyImage {
    url: String,
    height: Option<u32>,
    width: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyPlaylist {
    id: String,
    name: String,
    description: Option<String>,
    images: Vec<SpotifyImage>,
    tracks: SpotifyPlaylistTracks,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyPlaylistTracks {
    total: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyTrack {
    id: String,
    name: String,
    artists: Vec<SpotifyArtist>,
    album: SpotifyAlbum,
    duration_ms: u32,
    preview_url: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyArtist {
    id: String,
    name: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyAlbum {
    id: String,
    name: String,
    images: Vec<SpotifyImage>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResult {
    success: bool,
    user: Option<SpotifyUser>,
    error: Option<String>,
}

pub struct SpotifyState {
    pub tokens: Mutex<Option<SpotifyTokens>>,
    pub client_id: String,
    pub client_secret: String,
    pub redirect_uri: String,
}

impl SpotifyState {
    pub fn new() -> Self {
        Self {
            tokens: Mutex::new(None),
            client_id: std::env::var("SPOTIFY_CLIENT_ID").unwrap_or_default(),
            client_secret: std::env::var("SPOTIFY_CLIENT_SECRET").unwrap_or_default(),
            redirect_uri: "http://127.0.0.1:8888/callback".to_string(),
        }
    }
}

#[tauri::command]
pub async fn spotify_authenticate(
    state: State<'_, SpotifyState>,
    app_handle: tauri::AppHandle,
) -> Result<AuthResult, String> {
    let client_id = &state.client_id;
    let redirect_uri = &state.redirect_uri;

    if client_id.is_empty() {
        return Ok(AuthResult {
            success: false,
            user: None,
            error: Some("Missing Spotify credentials".to_string()),
        });
    }

    // Create authorization URL
    let scopes = vec![
        "user-read-private",
        "user-read-email",
        "playlist-read-private",
        "playlist-read-collaborative",
        "streaming",
        "user-read-playback-state",
        "user-modify-playback-state",
    ];

    let auth_url = format!(
        "https://accounts.spotify.com/authorize?response_type=code&client_id={}&scope={}&redirect_uri={}&state={}",
        client_id,
        scopes.join("%20"),
        urlencoding::encode(redirect_uri),
        uuid::Uuid::new_v4()
    );

    // Open authorization URL in browser
    if let Err(e) = tauri_plugin_shell::ShellExt::shell(&app_handle).open(auth_url, None) {
        return Ok(AuthResult {
            success: false,
            user: None,
            error: Some(format!("Failed to open browser: {}", e)),
        });
    }

    // Start callback server and wait for authorization code
    match start_callback_server(&state).await {
        Ok(user) => Ok(AuthResult {
            success: true,
            user: Some(user),
            error: None,
        }),
        Err(e) => Ok(AuthResult {
            success: false,
            user: None,
            error: Some(e),
        }),
    }
}

async fn start_callback_server(_state: &SpotifyState) -> Result<SpotifyUser, String> {
    // This is a simplified version - in a real implementation, you'd need to:
    // 1. Start a local HTTP server on port 8888
    // 2. Wait for the callback with the authorization code
    // 3. Exchange the code for tokens
    // 4. Get user profile

    // For now, return a placeholder
    Err("Callback server not implemented yet".to_string())
}

#[tauri::command]
pub async fn spotify_get_playlists(
    state: State<'_, SpotifyState>,
) -> Result<Vec<SpotifyPlaylist>, String> {
    let access_token = {
        let tokens = state.tokens.lock().unwrap();
        if let Some(tokens) = tokens.as_ref() {
            tokens.access_token.clone()
        } else {
            return Err("Not authenticated".to_string());
        }
    };

    // Make API call to get playlists
    get_user_playlists(&access_token).await
}

async fn get_user_playlists(access_token: &str) -> Result<Vec<SpotifyPlaylist>, String> {
    let client = reqwest::Client::new();
    let response = client
        .get("https://api.spotify.com/v1/me/playlists")
        .header("Authorization", format!("Bearer {}", access_token))
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if response.status().is_success() {
        let _json: serde_json::Value = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse JSON: {}", e))?;

        // Parse playlists from response
        // This is simplified - you'd need to properly parse the Spotify API response
        Ok(vec![])
    } else {
        Err(format!("API request failed: {}", response.status()))
    }
}

#[tauri::command]
pub async fn spotify_get_access_token(state: State<'_, SpotifyState>) -> Result<String, String> {
    let tokens = state.tokens.lock().unwrap();
    if let Some(tokens) = tokens.as_ref() {
        Ok(tokens.access_token.clone())
    } else {
        Err("Not authenticated".to_string())
    }
}

#[tauri::command]
pub async fn spotify_logout(state: State<'_, SpotifyState>) -> Result<(), String> {
    let mut tokens = state.tokens.lock().unwrap();
    *tokens = None;
    Ok(())
}

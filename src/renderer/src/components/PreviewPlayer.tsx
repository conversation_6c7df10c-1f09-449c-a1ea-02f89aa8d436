import { useState, useEffect } from 'react'
import { Play, Pause, Volume2, VolumeX } from 'lucide-react'
import { previewPlayerService } from '../services/PreviewPlayerService'

interface PreviewPlayerProps {
  className?: string
}

export function PreviewPlayer({ className = '' }: PreviewPlayerProps) {
  const [currentTrack, setCurrentTrack] = useState<any>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(50)
  const [isMuted, setIsMuted] = useState(false)

  useEffect(() => {
    const handlePlay = (data: any) => {
      setCurrentTrack(data.track)
      setIsPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
    }

    const handleEnded = () => {
      setIsPlaying(false)
      setCurrentTrack(null)
      setCurrentTime(0)
    }

    const handleTimeUpdate = (data: any) => {
      setCurrentTime(data.currentTime)
      setDuration(data.duration)
    }

    const handleError = () => {
      setIsPlaying(false)
      setCurrentTrack(null)
    }

    previewPlayerService.on('play', handlePlay)
    previewPlayerService.on('pause', handlePause)
    previewPlayerService.on('ended', handleEnded)
    previewPlayerService.on('timeupdate', handleTimeUpdate)
    previewPlayerService.on('error', handleError)

    return () => {
      previewPlayerService.off('play', handlePlay)
      previewPlayerService.off('pause', handlePause)
      previewPlayerService.off('ended', handleEnded)
      previewPlayerService.off('timeupdate', handleTimeUpdate)
      previewPlayerService.off('error', handleError)
    }
  }, [])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handlePlayPause = () => {
    if (isPlaying) {
      previewPlayerService.pause()
    } else if (currentTrack) {
      previewPlayerService.resume()
    }
  }

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!currentTrack || !duration) return
    
    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const width = rect.width
    const percentage = clickX / width
    const newTime = percentage * duration
    
    previewPlayerService.seek(newTime)
  }

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    previewPlayerService.setVolume(newVolume / 100)
    setIsMuted(newVolume === 0)
  }

  const toggleMute = () => {
    if (isMuted) {
      setIsMuted(false)
      setVolume(50)
      previewPlayerService.setVolume(0.5)
    } else {
      setIsMuted(true)
      setVolume(0)
      previewPlayerService.setVolume(0)
    }
  }

  if (!currentTrack) {
    return (
      <div className={`bg-gray-900 text-white p-4 ${className}`}>
        <div className="flex items-center justify-center">
          <div className="text-sm text-gray-400 text-center">
            <div>🎵 Preview Mode Active</div>
            <div className="text-xs mt-1">Click play buttons for 30-second previews</div>
          </div>
        </div>
      </div>
    )
  }

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className={`bg-gray-900 text-white p-4 ${className}`}>
      <div className="flex items-center space-x-4">
        {/* Track Info */}
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div className="w-12 h-12 bg-gray-700 rounded flex-shrink-0">
            {currentTrack.album?.images?.[0]?.url ? (
              <img
                src={currentTrack.album.images[0].url}
                alt={currentTrack.album.name}
                className="w-full h-full object-cover rounded"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Play className="w-6 h-6 text-gray-400" />
              </div>
            )}
          </div>
          
          <div className="min-w-0 flex-1">
            <div className="text-sm font-medium truncate">
              {currentTrack.name}
            </div>
            <div className="text-xs text-gray-400 truncate">
              {currentTrack.artists?.map((artist: any) => artist.name).join(', ')}
            </div>
            <div className="text-xs text-blue-400">Preview Mode</div>
          </div>
        </div>

        {/* Player Controls */}
        <div className="flex items-center space-x-2">
          <button
            onClick={handlePlayPause}
            className="p-3 bg-white text-gray-900 hover:bg-gray-200 rounded-full transition-colors"
          >
            {isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Progress Bar */}
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <span className="text-xs text-gray-400 w-10 text-right">
            {formatTime(currentTime)}
          </span>
          
          <div
            className="flex-1 h-1 bg-gray-700 rounded-full cursor-pointer"
            onClick={handleSeek}
          >
            <div
              className="h-full bg-blue-400 rounded-full transition-all duration-100"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          
          <span className="text-xs text-gray-400 w-10">
            {formatTime(duration)}
          </span>
        </div>

        {/* Volume Control */}
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleMute}
            className="p-2 hover:bg-gray-700 rounded-full transition-colors"
          >
            {isMuted || volume === 0 ? (
              <VolumeX className="w-4 h-4" />
            ) : (
              <Volume2 className="w-4 h-4" />
            )}
          </button>
          
          <div className="w-20">
            <input
              type="range"
              min="0"
              max="100"
              value={volume}
              onChange={(e) => handleVolumeChange(Number(e.target.value))}
              className="w-full h-1 bg-gray-700 rounded-full appearance-none cursor-pointer slider"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

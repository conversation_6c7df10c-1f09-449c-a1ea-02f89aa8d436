import { useState, useEffect } from 'react'
import { Play, Pause, SkipBack, Ski<PERSON>For<PERSON>, Volume2, VolumeX } from 'lucide-react'

interface PlayerProps {
  player: any // Player from useSpotifyPlayer
  className?: string
}

export function Player({ player, className = '' }: PlayerProps) {
  const [volume, setVolume] = useState(50)
  const [isMuted, setIsMuted] = useState(false)
  const [previousVolume, setPreviousVolume] = useState(50)

  const { playerState, isConnected, error } = player

  // Format time in mm:ss
  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Handle volume change
  const handleVolumeChange = async (newVolume: number) => {
    setVolume(newVolume)
    if (newVolume === 0) {
      setIsMuted(true)
    } else {
      setIsMuted(false)
    }

    try {
      await player.setVolume(newVolume / 100)
    } catch (error) {
      console.error('Failed to set volume:', error)
    }
  }

  // Toggle mute
  const toggleMute = async () => {
    if (isMuted) {
      setIsMuted(false)
      setVolume(previousVolume)
      await player.setVolume(previousVolume / 100)
    } else {
      setPreviousVolume(volume)
      setIsMuted(true)
      setVolume(0)
      await player.setVolume(0)
    }
  }

  // Handle seek
  const handleSeek = async (e: React.MouseEvent<HTMLDivElement>) => {
    if (!playerState.currentTrack) return

    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const width = rect.width
    const percentage = clickX / width
    const newPosition = percentage * playerState.duration

    try {
      await player.seek(Math.floor(newPosition))
    } catch (error) {
      console.error('Failed to seek:', error)
    }
  }

  // Update volume from player state
  useEffect(() => {
    if (playerState.volume !== undefined) {
      setVolume(Math.round(playerState.volume * 100))
    }
  }, [playerState.volume])

  if (!isConnected || !playerState.currentTrack) {
    return (
      <div className={`bg-gray-900 text-white p-4 ${className}`}>
        <div className="flex items-center justify-center">
          <div className="text-sm text-gray-400 text-center">
            {error && error.includes('Web Playback SDK')
              ? (
                <div>
                  <div>🎵 Preview Mode Active</div>
                  <div className="text-xs mt-1">Click play buttons for 30-second previews</div>
                </div>
              )
              : !isConnected
                ? 'Connecting to Spotify player...'
                : 'No track playing'
            }
          </div>
        </div>
      </div>
    )
  }

  const { currentTrack, isPlaying, position, duration } = playerState
  const progressPercentage = duration > 0 ? (position / duration) * 100 : 0

  return (
    <div className={`bg-gray-900 text-white p-4 ${className}`}>
      {error && (
        <div className="mb-2 text-xs text-red-400 text-center">
          {error}
        </div>
      )}

      <div className="flex items-center space-x-4">
        {/* Track Info */}
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div className="w-12 h-12 bg-gray-700 rounded flex-shrink-0">
            {currentTrack.album?.images?.[0]?.url ? (
              <img
                src={currentTrack.album.images[0].url}
                alt={currentTrack.album.name}
                className="w-full h-full object-cover rounded"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Play className="w-6 h-6 text-gray-400" />
              </div>
            )}
          </div>

          <div className="min-w-0 flex-1">
            <div className="text-sm font-medium truncate">
              {currentTrack.name}
            </div>
            <div className="text-xs text-gray-400 truncate">
              {currentTrack.artists?.map((artist: any) => artist.name).join(', ')}
            </div>
          </div>
        </div>

        {/* Player Controls */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => player.previousTrack()}
            className="p-2 hover:bg-gray-700 rounded-full transition-colors"
            disabled={!isConnected}
          >
            <SkipBack className="w-4 h-4" />
          </button>

          <button
            onClick={() => player.togglePlay()}
            className="p-3 bg-white text-gray-900 hover:bg-gray-200 rounded-full transition-colors"
            disabled={!isConnected}
          >
            {isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5" />
            )}
          </button>

          <button
            onClick={() => player.nextTrack()}
            className="p-2 hover:bg-gray-700 rounded-full transition-colors"
            disabled={!isConnected}
          >
            <SkipForward className="w-4 h-4" />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <span className="text-xs text-gray-400 w-10 text-right">
            {formatTime(position)}
          </span>

          <div
            className="flex-1 h-1 bg-gray-700 rounded-full cursor-pointer"
            onClick={handleSeek}
          >
            <div
              className="h-full bg-white rounded-full transition-all duration-100"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>

          <span className="text-xs text-gray-400 w-10">
            {formatTime(duration)}
          </span>
        </div>

        {/* Volume Control */}
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleMute}
            className="p-2 hover:bg-gray-700 rounded-full transition-colors"
          >
            {isMuted || volume === 0 ? (
              <VolumeX className="w-4 h-4" />
            ) : (
              <Volume2 className="w-4 h-4" />
            )}
          </button>

          <div className="w-20">
            <input
              type="range"
              min="0"
              max="100"
              value={volume}
              onChange={(e) => handleVolumeChange(Number(e.target.value))}
              className="w-full h-1 bg-gray-700 rounded-full appearance-none cursor-pointer slider"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export interface IElectronAPI {
  loadPreferences: () => Promise<void>
  savePreferences: (preferences: any) => Promise<void>
}

export interface SpotifyAPI {
  authenticate: () => Promise<{ success: boolean; user?: any }>
  getPlaylists: () => Promise<any[]>
  getPlaylistTracks: (playlistId: string) => Promise<any[]>
  playTrack: (trackUri: string) => Promise<void>
  pauseTrack: () => Promise<void>
  resumeTrack: () => Promise<void>
  seekTrack: (position: number) => Promise<void>
  setVolume: (volume: number) => Promise<void>
  getCurrentState: () => Promise<any>
  getAccessToken: () => Promise<string>
  startPlayback: (deviceId: string, body: any) => Promise<void>
  logout: () => Promise<void>
}

export interface SoulseekAPI {
  connect: (username: string, password: string) => Promise<boolean>
  search: (query: string) => Promise<number>
  getSearchResults: (searchId: number) => Promise<any[]>
  download: (username: string, filename: string, filesize: number) => Promise<string>
  getDownloadProgress: (downloadId: string) => Promise<any>
  getDownloads: () => Promise<any[]>
  cancelDownload: (downloadId: string) => Promise<void>
}

export interface UtilsAPI {
  openExternal: (url: string) => Promise<void>
  showSaveDialog: (options: any) => Promise<any>
  showOpenDialog: (options: any) => Promise<any>
}

declare global {
  interface Window {
    electron: IElectronAPI
    api: {
      spotify: SpotifyAPI
      soulseek: SoulseekAPI
      utils: UtilsAPI
    }
  }
}

export { }

// Fallback preview player for when Web Playback SDK is not available
export class PreviewPlayerService {
  private audio: HTMLAudioElement | null = null;
  private currentTrack: any = null;
  private isPlaying = false;
  private listeners: Map<string, Set<Function>> = new Map();

  constructor() {
    this.audio = new Audio();
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.audio) return;

    this.audio.addEventListener('loadstart', () => {
      this.emit('loading', { track: this.currentTrack });
    });

    this.audio.addEventListener('canplay', () => {
      this.emit('ready', { track: this.currentTrack });
    });

    this.audio.addEventListener('play', () => {
      this.isPlaying = true;
      this.emit('play', { track: this.currentTrack });
    });

    this.audio.addEventListener('pause', () => {
      this.isPlaying = false;
      this.emit('pause', { track: this.currentTrack });
    });

    this.audio.addEventListener('ended', () => {
      this.isPlaying = false;
      this.emit('ended', { track: this.currentTrack });
    });

    this.audio.addEventListener('error', (e) => {
      this.emit('error', { 
        message: 'Failed to load preview',
        track: this.currentTrack,
        error: e 
      });
    });

    this.audio.addEventListener('timeupdate', () => {
      if (this.audio) {
        this.emit('timeupdate', {
          currentTime: this.audio.currentTime,
          duration: this.audio.duration,
          track: this.currentTrack
        });
      }
    });
  }

  async playPreview(track: any): Promise<void> {
    if (!track.preview_url) {
      throw new Error('No preview URL available for this track');
    }

    if (!this.audio) {
      throw new Error('Audio player not initialized');
    }

    // Stop current playback
    this.stop();

    this.currentTrack = track;
    this.audio.src = track.preview_url;
    
    try {
      await this.audio.play();
    } catch (error) {
      throw new Error('Failed to play preview: ' + (error as Error).message);
    }
  }

  pause(): void {
    if (this.audio && !this.audio.paused) {
      this.audio.pause();
    }
  }

  resume(): void {
    if (this.audio && this.audio.paused && this.currentTrack) {
      this.audio.play().catch(error => {
        this.emit('error', { 
          message: 'Failed to resume playback',
          error 
        });
      });
    }
  }

  stop(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
      this.audio.src = '';
    }
    this.currentTrack = null;
    this.isPlaying = false;
  }

  seek(time: number): void {
    if (this.audio && this.currentTrack) {
      this.audio.currentTime = time;
    }
  }

  setVolume(volume: number): void {
    if (this.audio) {
      this.audio.volume = Math.max(0, Math.min(1, volume));
    }
  }

  getVolume(): number {
    return this.audio?.volume || 0;
  }

  getCurrentTime(): number {
    return this.audio?.currentTime || 0;
  }

  getDuration(): number {
    return this.audio?.duration || 0;
  }

  isCurrentlyPlaying(): boolean {
    return this.isPlaying;
  }

  getCurrentTrack(): any {
    return this.currentTrack;
  }

  // Event system
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  off(event: string, callback?: Function): void {
    if (callback) {
      this.listeners.get(event)?.delete(callback);
    } else {
      this.listeners.delete(event);
    }
  }

  private emit(event: string, data: any): void {
    this.listeners.get(event)?.forEach(callback => callback(data));
  }

  destroy(): void {
    this.stop();
    if (this.audio) {
      // Remove all event listeners
      this.audio.removeEventListener('loadstart', () => {});
      this.audio.removeEventListener('canplay', () => {});
      this.audio.removeEventListener('play', () => {});
      this.audio.removeEventListener('pause', () => {});
      this.audio.removeEventListener('ended', () => {});
      this.audio.removeEventListener('error', () => {});
      this.audio.removeEventListener('timeupdate', () => {});
    }
    this.listeners.clear();
  }
}

// Singleton instance
export const previewPlayerService = new PreviewPlayerService();

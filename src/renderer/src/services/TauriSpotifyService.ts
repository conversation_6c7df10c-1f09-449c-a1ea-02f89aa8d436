import { invoke } from '@tauri-apps/api/core';

export interface SpotifyUser {
  id: string;
  display_name?: string;
  email?: string;
  images: SpotifyImage[];
}

export interface SpotifyImage {
  url: string;
  height?: number;
  width?: number;
}

export interface SpotifyPlaylist {
  id: string;
  name: string;
  description?: string;
  images: SpotifyImage[];
  tracks: {
    total: number;
  };
}

export interface SpotifyTrack {
  id: string;
  name: string;
  artists: SpotifyArtist[];
  album: SpotifyAlbum;
  duration_ms: number;
  preview_url?: string;
}

export interface SpotifyArtist {
  id: string;
  name: string;
}

export interface SpotifyAlbum {
  id: string;
  name: string;
  images: SpotifyImage[];
}

export interface AuthResult {
  success: boolean;
  user?: SpotifyUser;
  error?: string;
}

export class TauriSpotifyService {
  async authenticate(): Promise<AuthResult> {
    try {
      return await invoke<AuthResult>('spotify_authenticate');
    } catch (error) {
      console.error('Authentication error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  async getPlaylists(): Promise<SpotifyPlaylist[]> {
    try {
      return await invoke<SpotifyPlaylist[]>('spotify_get_playlists');
    } catch (error) {
      console.error('Get playlists error:', error);
      throw error;
    }
  }

  async getPlaylistTracks(playlistId: string): Promise<any[]> {
    try {
      // This would need to be implemented in the Rust backend
      return await invoke<any[]>('spotify_get_playlist_tracks', { playlistId });
    } catch (error) {
      console.error('Get playlist tracks error:', error);
      throw error;
    }
  }

  async getAccessToken(): Promise<string> {
    try {
      return await invoke<string>('spotify_get_access_token');
    } catch (error) {
      console.error('Get access token error:', error);
      throw error;
    }
  }

  async startPlayback(deviceId: string, body: any): Promise<void> {
    try {
      // This would need to be implemented in the Rust backend
      return await invoke('spotify_start_playback', { deviceId, body });
    } catch (error) {
      console.error('Start playback error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      return await invoke('spotify_logout');
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  // Placeholder methods for compatibility
  async playTrack(trackUri: string): Promise<void> {
    console.log('Playing track:', trackUri);
  }

  async pauseTrack(): Promise<void> {
    console.log('Pausing track');
  }

  async resumeTrack(): Promise<void> {
    console.log('Resuming track');
  }

  async seekTrack(position: number): Promise<void> {
    console.log('Seeking to position:', position);
  }

  async setVolume(volume: number): Promise<void> {
    console.log('Setting volume to:', volume);
  }

  async getCurrentState(): Promise<any> {
    console.log('Getting current state');
    return null;
  }
}

// Singleton instance
export const tauriSpotifyService = new TauriSpotifyService();

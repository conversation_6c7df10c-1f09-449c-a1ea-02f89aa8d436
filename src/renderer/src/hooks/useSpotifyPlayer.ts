import { useState, useEffect, useCallback } from 'react'
import { spotifyPlayerService } from '../services/SpotifyPlayerService'

interface PlayerState {
  isPlaying: boolean
  currentTrack: any | null
  position: number
  duration: number
  volume: number
  isReady: boolean
  deviceId: string | null
}

interface UseSpotifyPlayerReturn {
  playerState: PlayerState
  isConnected: boolean
  error: string | null

  // Player controls
  play: (uris?: string[], contextUri?: string) => Promise<void>
  pause: () => Promise<void>
  resume: () => Promise<void>
  togglePlay: () => Promise<void>
  seek: (position: number) => Promise<void>
  setVolume: (volume: number) => Promise<void>
  nextTrack: () => Promise<void>
  previousTrack: () => Promise<void>

  // Connection
  connect: () => Promise<void>
  disconnect: () => void

  // Utility
  setAccessToken: (token: string) => void
}

export function useSpotifyPlayer(): UseSpotifyPlayerReturn {
  const [playerState, setPlayerState] = useState<PlayerState>({
    isPlaying: false,
    currentTrack: null,
    position: 0,
    duration: 0,
    volume: 0.5,
    isReady: false,
    deviceId: null
  })

  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Update player state from SDK events
  const handlePlayerStateChanged = useCallback((state: any) => {
    if (state) {
      setPlayerState(prev => ({
        ...prev,
        isPlaying: !state.paused,
        currentTrack: state.track_window.current_track,
        position: state.position,
        duration: state.track_window.current_track?.duration_ms || 0
      }))
    }
  }, [])

  const handleReady = useCallback(({ device_id }: { device_id: string }) => {
    setPlayerState(prev => ({
      ...prev,
      isReady: true,
      deviceId: device_id
    }))
    setIsConnected(true)
    setError(null)
  }, [])

  const handleNotReady = useCallback(() => {
    setPlayerState(prev => ({
      ...prev,
      isReady: false,
      deviceId: null
    }))
    setIsConnected(false)
  }, [])

  const handleError = useCallback((errorData: { message: string }) => {
    // Only set error for actual errors, not compatibility messages
    if (!errorData.message.includes('disabled for Electron compatibility')) {
      setError(errorData.message)
      console.error('Spotify Player Error:', errorData.message)
    }
  }, [])

  useEffect(() => {
    // Set up event listeners
    spotifyPlayerService.on('player_state_changed', handlePlayerStateChanged)
    spotifyPlayerService.on('ready', handleReady)
    spotifyPlayerService.on('not_ready', handleNotReady)
    spotifyPlayerService.on('initialization_error', handleError)
    spotifyPlayerService.on('authentication_error', handleError)
    spotifyPlayerService.on('account_error', handleError)
    spotifyPlayerService.on('playback_error', handleError)

    return () => {
      // Clean up event listeners
      spotifyPlayerService.off('player_state_changed', handlePlayerStateChanged)
      spotifyPlayerService.off('ready', handleReady)
      spotifyPlayerService.off('not_ready', handleNotReady)
      spotifyPlayerService.off('initialization_error', handleError)
      spotifyPlayerService.off('authentication_error', handleError)
      spotifyPlayerService.off('account_error', handleError)
      spotifyPlayerService.off('playback_error', handleError)
    }
  }, [handlePlayerStateChanged, handleReady, handleNotReady, handleError])

  // Player control methods
  const play = useCallback(async (uris?: string[], contextUri?: string) => {
    try {
      setError(null)
      await spotifyPlayerService.play(uris, contextUri)
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to play track'
      setError(message)
      throw err
    }
  }, [])

  const pause = useCallback(async () => {
    try {
      setError(null)
      await spotifyPlayerService.pause()
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to pause'
      setError(message)
      throw err
    }
  }, [])

  const resume = useCallback(async () => {
    try {
      setError(null)
      await spotifyPlayerService.resume()
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to resume'
      setError(message)
      throw err
    }
  }, [])

  const togglePlay = useCallback(async () => {
    try {
      setError(null)
      await spotifyPlayerService.togglePlay()
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to toggle play'
      setError(message)
      throw err
    }
  }, [])

  const seek = useCallback(async (position: number) => {
    try {
      setError(null)
      await spotifyPlayerService.seek(position)
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to seek'
      setError(message)
      throw err
    }
  }, [])

  const setVolume = useCallback(async (volume: number) => {
    try {
      setError(null)
      await spotifyPlayerService.setVolume(volume)
      setPlayerState(prev => ({ ...prev, volume }))
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to set volume'
      setError(message)
      throw err
    }
  }, [])

  const nextTrack = useCallback(async () => {
    try {
      setError(null)
      await spotifyPlayerService.nextTrack()
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to skip to next track'
      setError(message)
      throw err
    }
  }, [])

  const previousTrack = useCallback(async () => {
    try {
      setError(null)
      await spotifyPlayerService.previousTrack()
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to skip to previous track'
      setError(message)
      throw err
    }
  }, [])

  const connect = useCallback(async () => {
    try {
      setError(null)
      const success = await spotifyPlayerService.connect()
      if (!success) {
        console.warn('Web Playback SDK connection failed, using preview mode')
        setError('Web Playback SDK not available - using preview mode')
        return false
      }
      return true
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to connect'
      console.warn('Player connection error:', message)
      setError('Web Playback SDK not available - using preview mode')
      return false
    }
  }, [])

  const disconnect = useCallback(() => {
    spotifyPlayerService.disconnect()
    setIsConnected(false)
    setPlayerState(prev => ({
      ...prev,
      isReady: false,
      deviceId: null
    }))
  }, [])

  const setAccessToken = useCallback((token: string) => {
    spotifyPlayerService.setAccessToken(token)
  }, [])

  return {
    playerState,
    isConnected,
    error,

    // Controls
    play,
    pause,
    resume,
    togglePlay,
    seek,
    setVolume,
    nextTrack,
    previousTrack,

    // Connection
    connect,
    disconnect,

    // Utility
    setAccessToken
  }
}

import { useState } from 'react'
import { Music, Download, Settings, LogIn } from 'lucide-react'
import { useSpotify } from './hooks/useSpotify'
import { PlaylistView } from './components/PlaylistView'
import { Player } from './components/Player'
import { PreviewPlayer } from './components/PreviewPlayer'

function App(): JSX.Element {
  const [activeTab, setActiveTab] = useState('playlists')
  const {
    isAuthenticated,
    user,
    playlists,
    selectedPlaylist,
    tracks,
    loading,
    error,
    player,
    authenticate,
    logout,
    selectPlaylist,
    playTrack,
    setError
  } = useSpotify()

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-400 to-blue-600 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-white rounded-lg shadow-lg p-6">
          <div className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
              <Music className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold mb-2">Spotify-Soulseek App</h1>
            <p className="text-gray-600 mb-6">
              Connect your Spotify account to get started with music discovery and downloads
            </p>
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}
            <button
              onClick={authenticate}
              disabled={loading}
              className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md flex items-center justify-center disabled:opacity-50"
            >
              <LogIn className="w-4 h-4 mr-2" />
              {loading ? 'Connecting...' : 'Connect with Spotify'}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="border-b bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Music className="w-6 h-6 text-green-500" />
            <h1 className="text-xl font-bold">Spotify-Soulseek</h1>
          </div>
          <div className="flex items-center space-x-4">
            {user && (
              <span className="text-sm text-gray-600">
                Welcome, {user.display_name}
              </span>
            )}
            {player.isConnected ? (
              <div className="text-xs text-green-600">
                🎵 Full Player Ready
              </div>
            ) : (
              <div className="text-xs text-blue-600">
                🎵 Preview Mode
              </div>
            )}
            <button
              onClick={logout}
              className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6 flex-1">
        <div className="w-full">
          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
            <button
              onClick={() => setActiveTab('playlists')}
              className={`flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md ${activeTab === 'playlists'
                ? 'bg-white shadow-sm text-gray-900'
                : 'text-gray-600 hover:text-gray-900'
                }`}
            >
              <Music className="w-4 h-4 mr-2" />
              Playlists
            </button>
            <button
              onClick={() => setActiveTab('downloads')}
              className={`flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md ${activeTab === 'downloads'
                ? 'bg-white shadow-sm text-gray-900'
                : 'text-gray-600 hover:text-gray-900'
                }`}
            >
              <Download className="w-4 h-4 mr-2" />
              Downloads
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md ${activeTab === 'settings'
                ? 'bg-white shadow-sm text-gray-900'
                : 'text-gray-600 hover:text-gray-900'
                }`}
            >
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </button>
          </div>

          {/* Tab Content */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
                <button
                  onClick={() => setError(null)}
                  className="ml-2 text-red-500 hover:text-red-700"
                >
                  ×
                </button>
              </div>
            )}

            {activeTab === 'playlists' && (
              <div>
                <h2 className="text-xl font-semibold mb-2">Your Playlists</h2>
                <p className="text-gray-600 mb-6">
                  Browse your Spotify playlists and search for tracks on Soulseek
                </p>
                <PlaylistView
                  playlists={playlists}
                  selectedPlaylist={selectedPlaylist}
                  tracks={tracks}
                  loading={loading}
                  onSelectPlaylist={selectPlaylist}
                  onPlayTrack={playTrack}
                  playerError={player.error}
                />
              </div>
            )}

            {activeTab === 'downloads' && (
              <div>
                <h2 className="text-xl font-semibold mb-2">Download Manager</h2>
                <p className="text-gray-600 mb-6">
                  Track your Soulseek downloads and manage your music library
                </p>
                <div className="text-center py-8 text-gray-500">
                  Download manager coming soon...
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div>
                <h2 className="text-xl font-semibold mb-2">Settings</h2>
                <p className="text-gray-600 mb-6">
                  Configure your app preferences and Soulseek connection
                </p>
                <div className="text-center py-8 text-gray-500">
                  Settings panel coming soon...
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Player */}
      {isAuthenticated && (
        player.isConnected ? (
          <Player player={player} className="border-t" />
        ) : (
          <PreviewPlayer className="border-t" />
        )
      )}
    </div>
  )
}

export default App

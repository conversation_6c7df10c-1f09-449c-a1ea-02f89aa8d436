import { <PERSON><PERSON><PERSON><PERSON>indow } from 'electron'
import SpotifyWebApi from 'spotify-web-api-node'
import Store from 'electron-store'
import { CallbackServer } from './callback-server'

interface SpotifyTokens {
  access_token: string
  refresh_token: string
  expires_in: number
  token_type: string
  scope: string
}

export class SpotifyService {
  private authWindow: BrowserWindow | null = null
  private spotifyApi: SpotifyWebApi
  private store: Store
  private clientId: string
  private clientSecret: string
  private redirectUri: string
  private static callbackServer: CallbackServer | null = null

  constructor() {
    this.store = new Store()

    // Use singleton pattern for callback server
    if (!SpotifyService.callbackServer) {
      SpotifyService.callbackServer = new CallbackServer()
    }

    // Get credentials from environment variables
    this.clientId = process.env.SPOTIFY_CLIENT_ID || ''
    this.clientSecret = process.env.SPOTIFY_CLIENT_SECRET || ''
    this.redirectUri = SpotifyService.callbackServer!.getCallbackUrl()

    // Validate required environment variables
    if (!this.clientId || !this.clientSecret) {
      console.error('Missing Spotify credentials in environment variables')
      console.error('Please set SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET in your .env file')
    }

    this.spotifyApi = new SpotifyWebApi({
      clientId: this.clientId,
      clientSecret: this.clientSecret,
      redirectUri: this.redirectUri
    })

    // Load stored tokens on initialization
    this.loadStoredTokens()
  }

  async authenticate(): Promise<{ success: boolean; user?: any; error?: string }> {
    try {
      // Validate credentials first
      if (!this.clientId || !this.clientSecret) {
        return {
          success: false,
          error: 'Missing Spotify credentials. Please check your .env file.'
        }
      }

      // Check if we have valid tokens first
      if (await this.hasValidTokens()) {
        const user = await this.getCurrentUser()
        return { success: true, user }
      }

      // Start OAuth flow
      return await this.startOAuthFlow()
    } catch (error) {
      console.error('Authentication error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      }
    }
  }

  private async startOAuthFlow(): Promise<{ success: boolean; user?: any }> {
    try {
      // Start the callback server
      await SpotifyService.callbackServer!.start()

      const scopes = [
        'user-read-private',
        'user-read-email',
        'playlist-read-private',
        'playlist-read-collaborative',
        'streaming',
        'user-read-playback-state',
        'user-modify-playback-state'
      ]

      const authorizeURL = this.spotifyApi.createAuthorizeURL(scopes, 'state-key')

      return new Promise((resolve, reject) => {
        // Close any existing auth window
        if (this.authWindow) {
          this.authWindow.close()
          this.authWindow = null
        }

        this.authWindow = new BrowserWindow({
          width: 800,
          height: 600,
          show: true,
          title: 'Spotify Authentication',
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true
          }
        })

        this.authWindow.loadURL(authorizeURL)

        // Handle successful navigation to callback URL
        const handleCallback = async (url: string) => {
          if (url.startsWith(this.redirectUri)) {
            const urlParams = new URL(url).searchParams
            const code = urlParams.get('code')
            const error = urlParams.get('error')

            if (error) {
              this.cleanup()
              reject(new Error(`Spotify OAuth error: ${error}`))
              return
            }

            if (code) {
              try {
                const data = await this.spotifyApi.authorizationCodeGrant(code)
                this.storeTokens(data.body)
                this.spotifyApi.setAccessToken(data.body.access_token)
                this.spotifyApi.setRefreshToken(data.body.refresh_token)

                const user = await this.getCurrentUser()
                this.cleanup()
                resolve({ success: true, user })
              } catch (err) {
                console.error('Error during token exchange:', err)
                this.cleanup()
                reject(err)
              }
            }
          }
        }

        // Handle both will-navigate and did-navigate events
        this.authWindow.webContents.on('will-navigate', async (event, url) => {
          if (url.startsWith(this.redirectUri)) {
            event.preventDefault()
            await handleCallback(url)
          }
        })

        this.authWindow.webContents.on('did-navigate', async (event, url) => {
          if (url.startsWith(this.redirectUri)) {
            await handleCallback(url)
          }
        })

        // Handle window closed
        this.authWindow.on('closed', () => {
          this.authWindow = null
          this.cleanup()
          reject(new Error('Authentication window was closed'))
        })

        // Set a timeout for the authentication process
        setTimeout(() => {
          if (this.authWindow) {
            this.cleanup()
            reject(new Error('Authentication timeout'))
          }
        }, 300000) // 5 minutes timeout
      })
    } catch (error) {
      this.cleanup()
      throw error
    }
  }

  private cleanup(): void {
    if (this.authWindow) {
      this.authWindow.close()
      this.authWindow = null
    }
    // Note: We don't stop the callback server here as it might be needed for multiple attempts
  }

  private async hasValidTokens(): Promise<boolean> {
    const tokens = this.store.get('spotify_tokens') as SpotifyTokens
    if (!tokens) return false

    // Check if token is expired
    const expiryTime = this.store.get('spotify_token_expiry') as number
    if (Date.now() > expiryTime) {
      // Try to refresh token
      return await this.refreshTokens()
    }

    this.spotifyApi.setAccessToken(tokens.access_token)
    this.spotifyApi.setRefreshToken(tokens.refresh_token)
    return true
  }

  private async refreshTokens(): Promise<boolean> {
    try {
      const refreshToken = this.store.get('spotify_refresh_token') as string
      if (!refreshToken) return false

      this.spotifyApi.setRefreshToken(refreshToken)
      const data = await this.spotifyApi.refreshAccessToken()

      this.storeTokens(data.body)
      this.spotifyApi.setAccessToken(data.body.access_token)

      return true
    } catch (error) {
      console.error('Token refresh error:', error)
      return false
    }
  }

  private storeTokens(tokens: any): void {
    this.store.set('spotify_tokens', tokens)
    this.store.set('spotify_token_expiry', Date.now() + (tokens.expires_in * 1000))
    if (tokens.refresh_token) {
      this.store.set('spotify_refresh_token', tokens.refresh_token)
    }
  }

  private loadStoredTokens(): void {
    const tokens = this.store.get('spotify_tokens') as SpotifyTokens
    if (tokens) {
      this.spotifyApi.setAccessToken(tokens.access_token)
      const refreshToken = this.store.get('spotify_refresh_token') as string
      if (refreshToken) {
        this.spotifyApi.setRefreshToken(refreshToken)
      }
    }
  }

  private async getCurrentUser(): Promise<any> {
    try {
      const data = await this.spotifyApi.getMe()
      return data.body
    } catch (error) {
      console.error('Error fetching user:', error)
      throw error
    }
  }

  async getPlaylists(): Promise<any[]> {
    try {
      const data = await this.spotifyApi.getUserPlaylists()
      return data.body.items
    } catch (error) {
      console.error('Error fetching playlists:', error)
      throw error
    }
  }

  async getPlaylistTracks(playlistId: string): Promise<any[]> {
    try {
      const data = await this.spotifyApi.getPlaylistTracks(playlistId)
      return data.body.items
    } catch (error) {
      console.error('Error fetching playlist tracks:', error)
      throw error
    }
  }

  async playTrack(trackUri: string): Promise<void> {
    console.log('Playing track:', trackUri)
    // This method is now handled by the Web Playback SDK in the renderer
    // The actual playback is initiated through startPlayback method
  }

  async pauseTrack(): Promise<void> {
    console.log('Pausing track')
    // Handled by Web Playback SDK in renderer
  }

  async resumeTrack(): Promise<void> {
    console.log('Resuming track')
    // Handled by Web Playback SDK in renderer
  }

  async seekTrack(position: number): Promise<void> {
    console.log('Seeking to position:', position)
    // Handled by Web Playback SDK in renderer
  }

  async setVolume(volume: number): Promise<void> {
    console.log('Setting volume to:', volume)
    // Handled by Web Playback SDK in renderer
  }

  async getAccessToken(): Promise<string> {
    const tokens = this.store.get('spotify_tokens') as SpotifyTokens
    if (!tokens) {
      throw new Error('No access token available')
    }

    // Check if token is expired and refresh if needed
    const expiryTime = this.store.get('spotify_token_expiry') as number
    if (Date.now() > expiryTime) {
      const refreshed = await this.refreshTokens()
      if (!refreshed) {
        throw new Error('Failed to refresh access token')
      }
      const newTokens = this.store.get('spotify_tokens') as SpotifyTokens
      return newTokens.access_token
    }

    return tokens.access_token
  }

  async startPlayback(deviceId: string, body: any): Promise<void> {
    try {
      await this.spotifyApi.play({
        device_id: deviceId,
        ...body
      })
    } catch (error) {
      console.error('Error starting playback:', error)
      throw error
    }
  }

  async getCurrentState(): Promise<any> {
    // Mock current state
    return {
      is_playing: false,
      progress_ms: 0,
      item: null
    }
  }

  async logout(): Promise<void> {
    console.log('Logging out')
    // Clear stored tokens and reset state
    this.store.delete('spotify_tokens')
    this.store.delete('spotify_token_expiry')
    this.store.delete('spotify_refresh_token')

    // Reset API instance
    this.spotifyApi.resetAccessToken()
    this.spotifyApi.resetRefreshToken()
  }

  // Debug method to clear all tokens
  async clearTokens(): Promise<void> {
    console.log('Clearing all stored tokens...')
    this.store.delete('spotify_tokens')
    this.store.delete('spotify_token_expiry')
    this.store.delete('spotify_refresh_token')
    this.spotifyApi.resetAccessToken()
    this.spotifyApi.resetRefreshToken()
  }
}

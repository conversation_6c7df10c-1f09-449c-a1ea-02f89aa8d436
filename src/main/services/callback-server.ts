import { createServer, Server } from 'http'
import { URL } from 'url'

export class CallbackServer {
  private server: Server | null = null
  private port: number = 8888
  private isRunning: boolean = false

  start(): Promise<void> {
    if (this.isRunning) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      this.server = createServer((req, res) => {
        if (req.url?.startsWith('/callback')) {
          // Handle the callback
          res.writeHead(200, { 'Content-Type': 'text/html' })
          res.end(`
            <html>
              <head><title>Spotify Authentication</title></head>
              <body>
                <h1>Authentication Successful!</h1>
                <p>You can now close this window and return to the app.</p>
                <script>
                  setTimeout(() => {
                    window.close();
                  }, 2000);
                </script>
              </body>
            </html>
          `)
        } else {
          res.writeHead(404)
          res.end('Not found')
        }
      })

      this.server.listen(this.port, '127.0.0.1', () => {
        console.log(`Callback server running on http://127.0.0.1:${this.port}`)
        this.isRunning = true
        resolve()
      })

      this.server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
          console.log(`Port ${this.port} already in use, assuming server is already running`)
          this.isRunning = true
          resolve()
        } else {
          reject(error)
        }
      })
    })
  }

  stop(): void {
    if (this.server && this.isRunning) {
      this.server.close()
      this.server = null
      this.isRunning = false
      console.log(`Callback server stopped`)
    }
  }

  getCallbackUrl(): string {
    return `http://127.0.0.1:${this.port}/callback`
  }
}

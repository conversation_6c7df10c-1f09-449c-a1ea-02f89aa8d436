import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// Custom APIs for renderer
const api = {
  // Spotify API methods
  spotify: {
    authenticate: () => ipcRenderer.invoke('spotify:authenticate'),
    getPlaylists: () => ipcRenderer.invoke('spotify:getPlaylists'),
    getPlaylistTracks: (playlistId: string) =>
      ipcRenderer.invoke('spotify:getPlaylistTracks', playlistId),
    playTrack: (trackUri: string) =>
      ipcRenderer.invoke('spotify:playTrack', trackUri),
    pauseTrack: () => ipcRenderer.invoke('spotify:pauseTrack'),
    resumeTrack: () => ipcRenderer.invoke('spotify:resumeTrack'),
    seekTrack: (position: number) =>
      ipcRenderer.invoke('spotify:seekTrack', position),
    setVolume: (volume: number) =>
      ipcRenderer.invoke('spotify:setVolume', volume),
    getCurrentState: () => ipcRenderer.invoke('spotify:getCurrentState'),
    getAccessToken: () => ipcRenderer.invoke('spotify:getAccessToken'),
    startPlayback: (deviceId: string, body: any) =>
      ipcRenderer.invoke('spotify:startPlayback', deviceId, body),
    logout: () => ipcRenderer.invoke('spotify:logout')
  },

  // Soulseek API methods
  soulseek: {
    connect: (username: string, password: string) =>
      ipcRenderer.invoke('soulseek:connect', username, password),
    search: (query: string) =>
      ipcRenderer.invoke('soulseek:search', query),
    getSearchResults: (searchId: number) =>
      ipcRenderer.invoke('soulseek:getSearchResults', searchId),
    download: (username: string, filename: string, filesize: number) =>
      ipcRenderer.invoke('soulseek:download', username, filename, filesize),
    getDownloadProgress: (downloadId: string) =>
      ipcRenderer.invoke('soulseek:getDownloadProgress', downloadId),
    getDownloads: () => ipcRenderer.invoke('soulseek:getDownloads'),
    cancelDownload: (downloadId: string) =>
      ipcRenderer.invoke('soulseek:cancelDownload', downloadId)
  },

  // Utility methods
  utils: {
    openExternal: (url: string) => ipcRenderer.invoke('utils:openExternal', url),
    showSaveDialog: (options: any) => ipcRenderer.invoke('utils:showSaveDialog', options),
    showOpenDialog: (options: any) => ipcRenderer.invoke('utils:showOpenDialog', options)
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}

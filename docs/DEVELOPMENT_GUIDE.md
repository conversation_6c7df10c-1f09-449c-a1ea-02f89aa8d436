# Development Guide - Spotify-Soulseek Desktop App

## Prerequisites

### Required Software
- **Node.js** (v18 or higher)
- **Python** (v3.8 or higher)
- **Git**
- **VS Code** (recommended) with extensions:
  - TypeScript and JavaScript Language Features
  - Python
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets

### Development Environment Setup

1. **<PERSON><PERSON> and Initialize Repository**
   ```bash
   git clone https://github.com/odmustafa/spotify-api.git
   cd spotify-api
   git branch -M main
   git push -u origin main
   ```

2. **Install Node.js Dependencies**
   ```bash
   npm install
   ```

3. **Set up Python Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r python/requirements.txt
   ```

## Project Initialization Steps

### Step 1: Create Electron + React + TypeScript Project
```bash
# Initialize with electron-vite template
npm create electron-vite@latest spotify-soulseek-app -- --template react-ts
cd spotify-soulseek-app
npm install
```

### Step 2: Install Additional Dependencies
```bash
# UI and styling
npm install @radix-ui/react-slot @radix-ui/react-dialog @radix-ui/react-dropdown-menu
npm install class-variance-authority clsx tailwind-merge lucide-react
npm install tailwindcss-animate

# Spotify integration
npm install spotify-web-api-node
npm install @types/spotify-web-api-node

# Utilities
npm install axios sqlite3 electron-store
npm install @types/sqlite3

# Development dependencies
npm install -D @types/node
```

### Step 3: Configure Shadcn/ui
```bash
npx shadcn-ui@latest init
npx shadcn-ui@latest add button
npx shadcn-ui@latest add card
npx shadcn-ui@latest add dialog
npx shadcn-ui@latest add dropdown-menu
npx shadcn-ui@latest add input
npx shadcn-ui@latest add label
npx shadcn-ui@latest add progress
npx shadcn-ui@latest add table
npx shadcn-ui@latest add tabs
```

## Spotify API Setup

### 1. Create Spotify App
1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Create a new app
3. Note down Client ID and Client Secret
4. Add redirect URI: `http://localhost:3000/callback`

### 2. Environment Configuration
Create `.env` file:
```env
SPOTIFY_CLIENT_ID=your_client_id_here
SPOTIFY_CLIENT_SECRET=your_client_secret_here
SPOTIFY_REDIRECT_URI=http://localhost:3000/callback
```

## Soulseek Integration Development

### Understanding Soulseek Protocol
Based on Nicotine+ implementation, key components:

1. **Connection Management**
   - TCP socket connections to Soulseek server
   - Peer-to-peer connections for file transfers
   - Message protocol handling

2. **Search Implementation**
   - Search request formatting
   - Result parsing and display
   - File metadata extraction

3. **Download Management**
   - File transfer protocol
   - Progress tracking
   - Error handling

### Python Service Architecture
```python
# python/soulseek_client/client.py
class SoulseekClient:
    def __init__(self):
        self.server_socket = None
        self.peers = {}
        self.searches = {}
    
    async def connect(self, username, password):
        # Connect to Soulseek server
        pass
    
    async def search(self, query):
        # Perform search and return results
        pass
    
    async def download(self, peer, filename):
        # Download file from peer
        pass
```

## Development Workflow

### 1. Start Development Environment
```bash
# Terminal 1: Start Electron app
npm run dev

# Terminal 2: Activate Python environment
source venv/bin/activate
python python/soulseek_client/server.py
```

### 2. Code Structure Guidelines

#### TypeScript/React Components
```typescript
// src/renderer/src/components/playlist/PlaylistView.tsx
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface Track {
  id: string
  name: string
  artists: string[]
  preview_url?: string
}

export const PlaylistView: React.FC = () => {
  // Component implementation
}
```

#### Electron IPC Communication
```typescript
// src/main/ipc/spotify-handlers.ts
import { ipcMain } from 'electron'

ipcMain.handle('spotify:authenticate', async () => {
  // Handle Spotify authentication
})

ipcMain.handle('spotify:getPlaylists', async () => {
  // Fetch user playlists
})
```

#### Python Service Integration
```typescript
// src/main/services/soulseek-service.ts
import { spawn } from 'child_process'

export class SoulseekService {
  private pythonProcess: any

  async startService() {
    this.pythonProcess = spawn('python', ['python/soulseek_client/server.py'])
  }

  async search(query: string) {
    // Send search request to Python service
  }
}
```

## Testing Strategy

### Unit Testing
```bash
# Install testing dependencies
npm install -D vitest @testing-library/react @testing-library/jest-dom

# Run tests
npm run test
```

### Integration Testing
- Test Spotify API integration
- Test Soulseek search functionality
- Test file download operations

### End-to-End Testing
```bash
# Install Playwright
npm install -D @playwright/test

# Run E2E tests
npm run test:e2e
```

## Build and Distribution

### Development Build
```bash
npm run build
```

### Production Build
```bash
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux
```

## Debugging

### Electron Main Process
- Use VS Code debugger with Electron
- Console logging in main process

### Renderer Process
- Chrome DevTools (Ctrl+Shift+I)
- React DevTools extension

### Python Service
- Use Python debugger (pdb)
- Logging to file for service communication

## Common Issues and Solutions

### 1. Spotify Authentication Issues
- Check redirect URI configuration
- Verify client credentials
- Ensure proper CORS handling

### 2. Soulseek Connection Problems
- Check network connectivity
- Verify protocol implementation
- Handle connection timeouts

### 3. File Download Issues
- Implement proper error handling
- Check file permissions
- Handle partial downloads

## Code Quality

### ESLint Configuration
```json
{
  "extends": ["@electron-toolkit/eslint-config-ts"],
  "rules": {
    "@typescript-eslint/explicit-function-return-type": "error"
  }
}
```

### Prettier Configuration
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5"
}
```

## Git Workflow

### Branch Strategy
- `main`: Production-ready code
- `develop`: Integration branch
- `feature/*`: Feature development
- `hotfix/*`: Critical fixes

### Commit Convention
```
feat: add Spotify authentication
fix: resolve playlist loading issue
docs: update development guide
style: format code with prettier
refactor: improve search performance
test: add unit tests for player component
```

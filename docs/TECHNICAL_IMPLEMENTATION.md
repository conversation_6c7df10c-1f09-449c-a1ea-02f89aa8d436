# Technical Implementation Guide - Spotify-Soulseek Desktop App

## Architecture Deep Dive

### Application Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Electron Main Process                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Spotify API   │  │  Soulseek Core  │  │ File System │ │
│  │    Service      │  │    Service      │  │   Manager   │ │
│  │                 │  │                 │  │             │ │
│  │ • OAuth Flow    │  │ • Protocol Impl │  │ • Downloads │ │
│  │ • Token Mgmt    │  │ • Search Engine │  │ • Metadata  │ │
│  │ • API Calls     │  │ • P2P Transfers │  │ • Storage   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│                              │                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                IPC Communication Layer                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  Electron Renderer Process                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                React Application                        │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │ │
│  │  │    Auth     │  │  Playlist   │  │    Soulseek     │ │ │
│  │  │ Components  │  │ Components  │  │   Components    │ │ │
│  │  │             │  │             │  │                 │ │ │
│  │  │ • Login UI  │  │ • List View │  │ • Search UI     │ │ │
│  │  │ • Token UI  │  │ • Track UI  │  │ • Results UI    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │ │
│  │  │   Player    │  │   Search    │  │    Download     │ │ │
│  │  │ Components  │  │ Components  │  │   Components    │ │ │
│  │  │             │  │             │  │                 │ │ │
│  │  │ • Controls  │  │ • Query UI  │  │ • Progress UI   │ │ │
│  │  │ • Progress  │  │ • Filter UI │  │ • Queue UI      │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Data Flow Architecture
```
User Action → React Component → IPC Call → Main Process Service → External API/Service
     ↓                                                                      ↓
Response ← React State Update ← IPC Response ← Service Response ← API Response
```

## Spotify Integration Implementation

### 1. Authentication Service
```typescript
// src/main/services/spotify-service.ts
import SpotifyWebApi from 'spotify-web-api-node'
import { BrowserWindow } from 'electron'
import Store from 'electron-store'

interface SpotifyTokens {
  access_token: string
  refresh_token: string
  expires_in: number
  token_type: string
  scope: string
}

export class SpotifyService {
  private spotifyApi: SpotifyWebApi
  private store: Store
  private authWindow: BrowserWindow | null = null

  constructor() {
    this.store = new Store()
    this.spotifyApi = new SpotifyWebApi({
      clientId: process.env.SPOTIFY_CLIENT_ID,
      clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
      redirectUri: process.env.SPOTIFY_REDIRECT_URI
    })
    
    // Load stored tokens
    this.loadStoredTokens()
  }

  async authenticate(): Promise<boolean> {
    try {
      // Check if we have valid tokens
      if (await this.hasValidTokens()) {
        return true
      }

      // Start OAuth flow
      const authUrl = this.spotifyApi.createAuthorizeURL([
        'user-read-private',
        'user-read-email',
        'playlist-read-private',
        'playlist-read-collaborative',
        'streaming',
        'user-read-playback-state',
        'user-modify-playback-state'
      ], 'state-key')

      return new Promise((resolve, reject) => {
        this.authWindow = new BrowserWindow({
          width: 800,
          height: 600,
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true
          }
        })

        this.authWindow.loadURL(authUrl)

        // Handle redirect
        this.authWindow.webContents.on('will-redirect', async (event, url) => {
          if (url.startsWith(process.env.SPOTIFY_REDIRECT_URI!)) {
            const urlParams = new URL(url).searchParams
            const code = urlParams.get('code')
            const error = urlParams.get('error')

            if (error) {
              reject(new Error(error))
              return
            }

            if (code) {
              try {
                const data = await this.spotifyApi.authorizationCodeGrant(code)
                this.storeTokens(data.body)
                this.spotifyApi.setAccessToken(data.body.access_token)
                this.spotifyApi.setRefreshToken(data.body.refresh_token)
                resolve(true)
              } catch (err) {
                reject(err)
              }
            }

            this.authWindow?.close()
          }
        })

        this.authWindow.on('closed', () => {
          this.authWindow = null
          reject(new Error('Authentication cancelled'))
        })
      })
    } catch (error) {
      console.error('Authentication error:', error)
      return false
    }
  }

  private async hasValidTokens(): Promise<boolean> {
    const tokens = this.store.get('spotify_tokens') as SpotifyTokens
    if (!tokens) return false

    // Check if token is expired
    const expiryTime = this.store.get('spotify_token_expiry') as number
    if (Date.now() > expiryTime) {
      // Try to refresh token
      return await this.refreshTokens()
    }

    this.spotifyApi.setAccessToken(tokens.access_token)
    this.spotifyApi.setRefreshToken(tokens.refresh_token)
    return true
  }

  private async refreshTokens(): Promise<boolean> {
    try {
      const refreshToken = this.store.get('spotify_refresh_token') as string
      if (!refreshToken) return false

      this.spotifyApi.setRefreshToken(refreshToken)
      const data = await this.spotifyApi.refreshAccessToken()
      
      this.storeTokens(data.body)
      this.spotifyApi.setAccessToken(data.body.access_token)
      
      return true
    } catch (error) {
      console.error('Token refresh error:', error)
      return false
    }
  }

  private storeTokens(tokens: any): void {
    this.store.set('spotify_tokens', tokens)
    this.store.set('spotify_token_expiry', Date.now() + (tokens.expires_in * 1000))
    if (tokens.refresh_token) {
      this.store.set('spotify_refresh_token', tokens.refresh_token)
    }
  }

  async getPlaylists(): Promise<any[]> {
    try {
      const data = await this.spotifyApi.getUserPlaylists()
      return data.body.items
    } catch (error) {
      console.error('Error fetching playlists:', error)
      throw error
    }
  }

  async getPlaylistTracks(playlistId: string): Promise<any[]> {
    try {
      const data = await this.spotifyApi.getPlaylistTracks(playlistId)
      return data.body.items
    } catch (error) {
      console.error('Error fetching playlist tracks:', error)
      throw error
    }
  }
}
```

### 2. Web Playback SDK Integration
```typescript
// src/renderer/src/services/spotify-player.ts
export class SpotifyPlayer {
  private player: Spotify.Player | null = null
  private deviceId: string | null = null

  async initialize(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!window.Spotify) {
        // Load SDK
        const script = document.createElement('script')
        script.src = 'https://sdk.scdn.co/spotify-player.js'
        script.async = true
        document.body.appendChild(script)

        window.onSpotifyWebPlaybackSDKReady = () => {
          this.createPlayer(token).then(resolve).catch(reject)
        }
      } else {
        this.createPlayer(token).then(resolve).catch(reject)
      }
    })
  }

  private async createPlayer(token: string): Promise<void> {
    this.player = new window.Spotify.Player({
      name: 'Spotify-Soulseek App',
      getOAuthToken: (cb) => cb(token),
      volume: 0.5
    })

    // Error handling
    this.player.addListener('initialization_error', ({ message }) => {
      console.error('Failed to initialize:', message)
    })

    this.player.addListener('authentication_error', ({ message }) => {
      console.error('Failed to authenticate:', message)
    })

    this.player.addListener('account_error', ({ message }) => {
      console.error('Failed to validate Spotify account:', message)
    })

    this.player.addListener('playback_error', ({ message }) => {
      console.error('Failed to perform playback:', message)
    })

    // Ready
    this.player.addListener('ready', ({ device_id }) => {
      console.log('Ready with Device ID', device_id)
      this.deviceId = device_id
    })

    // Not Ready
    this.player.addListener('not_ready', ({ device_id }) => {
      console.log('Device ID has gone offline', device_id)
    })

    // Player state changed
    this.player.addListener('player_state_changed', (state) => {
      if (!state) return

      // Update UI with current state
      this.updatePlayerState(state)
    })

    // Connect to the player
    const connected = await this.player.connect()
    if (!connected) {
      throw new Error('Failed to connect to Spotify player')
    }
  }

  async playTrack(trackUri: string): Promise<void> {
    if (!this.deviceId) throw new Error('Player not ready')

    const response = await fetch(`https://api.spotify.com/v1/me/player/play?device_id=${this.deviceId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${this.getAccessToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        uris: [trackUri]
      })
    })

    if (!response.ok) {
      throw new Error('Failed to play track')
    }
  }

  async pause(): Promise<void> {
    await this.player?.pause()
  }

  async resume(): Promise<void> {
    await this.player?.resume()
  }

  async seek(position: number): Promise<void> {
    await this.player?.seek(position)
  }

  async setVolume(volume: number): Promise<void> {
    await this.player?.setVolume(volume)
  }

  private updatePlayerState(state: Spotify.PlaybackState): void {
    // Emit events or update global state
    window.dispatchEvent(new CustomEvent('spotify-player-state-changed', {
      detail: state
    }))
  }

  private getAccessToken(): string {
    // Get token from storage or context
    return localStorage.getItem('spotify_access_token') || ''
  }
}
```

## Soulseek Integration Implementation

### 1. Python Soulseek Client
```python
# python/soulseek_client/client.py
import asyncio
import socket
import struct
import json
import time
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict

@dataclass
class SearchResult:
    username: str
    filename: str
    filesize: int
    bitrate: int
    duration: int
    file_extension: str
    attributes: Dict

class SoulseekClient:
    def __init__(self):
        self.server_socket: Optional[socket.socket] = None
        self.server_host = "server.slsknet.org"
        self.server_port = 2242
        self.username = ""
        self.password = ""
        self.connected = False
        self.searches: Dict[int, Dict] = {}
        self.search_id_counter = 0
        self.downloads: Dict[str, Dict] = {}

    async def connect(self, username: str, password: str) -> bool:
        """Connect to Soulseek server"""
        try:
            self.username = username
            self.password = password
            
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.settimeout(10)
            await asyncio.get_event_loop().run_in_executor(
                None, self.server_socket.connect, (self.server_host, self.server_port)
            )
            
            # Send login message
            login_msg = self._pack_login_message(username, password)
            await self._send_message(login_msg)
            
            # Wait for login response
            response = await self._receive_message()
            success = self._parse_login_response(response)
            
            if success:
                self.connected = True
                # Start message handler
                asyncio.create_task(self._message_handler())
            
            return success
            
        except Exception as e:
            print(f"Connection error: {e}")
            return False

    def _pack_login_message(self, username: str, password: str) -> bytes:
        """Pack login message"""
        msg_type = 1  # LOGIN
        data = struct.pack('<I', len(username)) + username.encode('utf-8')
        data += struct.pack('<I', len(password)) + password.encode('utf-8')
        data += struct.pack('<I', 160)  # Version
        data += struct.pack('<I', len(username)) + username.encode('utf-8')  # Hash
        data += struct.pack('<I', 0)  # Minor version
        
        length = len(data) + 4
        return struct.pack('<I', length) + struct.pack('<I', msg_type) + data

    async def search(self, query: str) -> int:
        """Perform search and return search ID"""
        if not self.connected:
            raise Exception("Not connected to Soulseek server")
        
        search_id = self.search_id_counter
        self.search_id_counter += 1
        
        # Pack search message
        msg_type = 42  # FILE_SEARCH
        data = struct.pack('<I', search_id)
        data += struct.pack('<I', len(query))
        data += query.encode('utf-8')
        
        length = len(data) + 4
        message = struct.pack('<I', length) + struct.pack('<I', msg_type) + data
        
        await self._send_message(message)
        
        # Store search info
        self.searches[search_id] = {
            'query': query,
            'results': [],
            'timestamp': time.time()
        }
        
        return search_id

    async def get_search_results(self, search_id: int) -> List[SearchResult]:
        """Get search results for given search ID"""
        if search_id in self.searches:
            return self.searches[search_id]['results']
        return []

    async def download_file(self, username: str, filename: str, filesize: int) -> str:
        """Download file from peer"""
        download_id = f"{username}_{filename}_{int(time.time())}"
        
        # Store download info
        self.downloads[download_id] = {
            'username': username,
            'filename': filename,
            'filesize': filesize,
            'progress': 0,
            'status': 'starting'
        }
        
        try:
            # Connect to peer (simplified)
            peer_socket = await self._connect_to_peer(username)
            
            # Request file
            await self._request_file(peer_socket, filename)
            
            # Download file
            download_path = await self._download_file_data(
                peer_socket, filename, filesize, download_id
            )
            
            self.downloads[download_id]['status'] = 'completed'
            self.downloads[download_id]['path'] = download_path
            
            return download_path
            
        except Exception as e:
            self.downloads[download_id]['status'] = 'error'
            self.downloads[download_id]['error'] = str(e)
            raise

    async def _message_handler(self):
        """Handle incoming messages"""
        while self.connected:
            try:
                message = await self._receive_message()
                await self._process_message(message)
            except Exception as e:
                print(f"Message handler error: {e}")
                break

    async def _process_message(self, message: bytes):
        """Process incoming message"""
        if len(message) < 8:
            return
        
        length = struct.unpack('<I', message[:4])[0]
        msg_type = struct.unpack('<I', message[4:8])[0]
        data = message[8:]
        
        if msg_type == 43:  # FILE_SEARCH_RESULT
            await self._handle_search_result(data)

    async def _handle_search_result(self, data: bytes):
        """Handle search result message"""
        try:
            offset = 0
            
            # Parse username
            username_len = struct.unpack('<I', data[offset:offset+4])[0]
            offset += 4
            username = data[offset:offset+username_len].decode('utf-8')
            offset += username_len
            
            # Parse search token
            search_token = struct.unpack('<I', data[offset:offset+4])[0]
            offset += 4
            
            # Parse number of results
            num_results = struct.unpack('<I', data[offset:offset+4])[0]
            offset += 4
            
            results = []
            for _ in range(num_results):
                result, offset = self._parse_search_result_item(data, offset, username)
                results.append(result)
            
            # Add results to search
            if search_token in self.searches:
                self.searches[search_token]['results'].extend(results)
                
        except Exception as e:
            print(f"Error parsing search result: {e}")

    def _parse_search_result_item(self, data: bytes, offset: int, username: str) -> tuple:
        """Parse individual search result item"""
        # Parse filename
        filename_len = struct.unpack('<I', data[offset:offset+4])[0]
        offset += 4
        filename = data[offset:offset+filename_len].decode('utf-8', errors='ignore')
        offset += filename_len
        
        # Parse filesize
        filesize = struct.unpack('<Q', data[offset:offset+8])[0]
        offset += 8
        
        # Parse file extension
        ext_len = struct.unpack('<I', data[offset:offset+4])[0]
        offset += 4
        file_extension = data[offset:offset+ext_len].decode('utf-8', errors='ignore')
        offset += ext_len
        
        # Parse attributes
        num_attrs = struct.unpack('<I', data[offset:offset+4])[0]
        offset += 4
        
        attributes = {}
        bitrate = 0
        duration = 0
        
        for _ in range(num_attrs):
            attr_type = struct.unpack('<I', data[offset:offset+4])[0]
            offset += 4
            attr_value = struct.unpack('<I', data[offset:offset+4])[0]
            offset += 4
            
            attributes[attr_type] = attr_value
            
            if attr_type == 0:  # Bitrate
                bitrate = attr_value
            elif attr_type == 1:  # Duration
                duration = attr_value
        
        result = SearchResult(
            username=username,
            filename=filename,
            filesize=filesize,
            bitrate=bitrate,
            duration=duration,
            file_extension=file_extension,
            attributes=attributes
        )
        
        return result, offset

    async def _send_message(self, message: bytes):
        """Send message to server"""
        if self.server_socket:
            await asyncio.get_event_loop().run_in_executor(
                None, self.server_socket.send, message
            )

    async def _receive_message(self) -> bytes:
        """Receive message from server"""
        if not self.server_socket:
            raise Exception("No server connection")
        
        # Receive length header
        length_data = await asyncio.get_event_loop().run_in_executor(
            None, self.server_socket.recv, 4
        )
        
        if len(length_data) != 4:
            raise Exception("Failed to receive message length")
        
        length = struct.unpack('<I', length_data)[0]
        
        # Receive full message
        message = length_data
        remaining = length - 4
        
        while remaining > 0:
            chunk = await asyncio.get_event_loop().run_in_executor(
                None, self.server_socket.recv, min(remaining, 8192)
            )
            if not chunk:
                raise Exception("Connection closed")
            message += chunk
            remaining -= len(chunk)
        
        return message
```

### 2. Python Service Bridge
```python
# python/soulseek_client/server.py
import asyncio
import json
import sys
from client import SoulseekClient

class SoulseekService:
    def __init__(self):
        self.client = SoulseekClient()
        self.running = True

    async def start(self):
        """Start the service and listen for commands"""
        while self.running:
            try:
                # Read command from stdin
                line = await asyncio.get_event_loop().run_in_executor(
                    None, sys.stdin.readline
                )
                
                if not line:
                    break
                
                command = json.loads(line.strip())
                response = await self.handle_command(command)
                
                # Send response to stdout
                print(json.dumps(response), flush=True)
                
            except Exception as e:
                error_response = {
                    'id': command.get('id', 0),
                    'error': str(e)
                }
                print(json.dumps(error_response), flush=True)

    async def handle_command(self, command: dict) -> dict:
        """Handle incoming command"""
        cmd_type = command.get('type')
        cmd_id = command.get('id', 0)
        
        try:
            if cmd_type == 'connect':
                username = command['username']
                password = command['password']
                success = await self.client.connect(username, password)
                return {'id': cmd_id, 'result': success}
            
            elif cmd_type == 'search':
                query = command['query']
                search_id = await self.client.search(query)
                return {'id': cmd_id, 'result': search_id}
            
            elif cmd_type == 'get_search_results':
                search_id = command['search_id']
                results = await self.client.get_search_results(search_id)
                # Convert to dict for JSON serialization
                results_dict = [asdict(result) for result in results]
                return {'id': cmd_id, 'result': results_dict}
            
            elif cmd_type == 'download':
                username = command['username']
                filename = command['filename']
                filesize = command['filesize']
                path = await self.client.download_file(username, filename, filesize)
                return {'id': cmd_id, 'result': path}
            
            else:
                return {'id': cmd_id, 'error': f'Unknown command: {cmd_type}'}
                
        except Exception as e:
            return {'id': cmd_id, 'error': str(e)}

if __name__ == '__main__':
    service = SoulseekService()
    asyncio.run(service.start())
```

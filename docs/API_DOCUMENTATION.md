# API Documentation - Spotify-Soulseek Desktop App

## Spotify Web API Integration

### Authentication Flow

#### OAuth 2.0 Authorization Code Flow
```typescript
interface SpotifyAuthConfig {
  clientId: string
  clientSecret: string
  redirectUri: string
  scopes: string[]
}

const REQUIRED_SCOPES = [
  'user-read-private',
  'user-read-email',
  'playlist-read-private',
  'playlist-read-collaborative',
  'streaming',
  'user-read-playback-state',
  'user-modify-playback-state'
]
```

#### Authentication Methods
```typescript
// Generate authorization URL
function getAuthUrl(): string {
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: SPOTIFY_CLIENT_ID,
    scope: REQUIRED_SCOPES.join(' '),
    redirect_uri: SPOTIFY_REDIRECT_URI,
    state: generateRandomString(16)
  })
  return `https://accounts.spotify.com/authorize?${params}`
}

// Exchange code for tokens
async function exchangeCodeForTokens(code: string): Promise<SpotifyTokens> {
  const response = await fetch('https://accounts.spotify.com/api/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${Buffer.from(`${CLIENT_ID}:${CLIENT_SECRET}`).toString('base64')}`
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      redirect_uri: SPOTIFY_REDIRECT_URI
    })
  })
  return response.json()
}
```

### Spotify Web API Endpoints

#### User Profile
```typescript
// GET /v1/me
interface SpotifyUser {
  id: string
  display_name: string
  email: string
  images: SpotifyImage[]
  country: string
  product: string // 'premium' | 'free'
}
```

#### Playlists
```typescript
// GET /v1/me/playlists
interface SpotifyPlaylist {
  id: string
  name: string
  description: string
  images: SpotifyImage[]
  tracks: {
    total: number
    href: string
  }
  owner: {
    id: string
    display_name: string
  }
  public: boolean
  collaborative: boolean
}

// GET /v1/playlists/{playlist_id}/tracks
interface SpotifyTrack {
  id: string
  name: string
  artists: SpotifyArtist[]
  album: SpotifyAlbum
  duration_ms: number
  preview_url: string | null
  external_urls: {
    spotify: string
  }
  popularity: number
  explicit: boolean
}
```

### Spotify Web Playback SDK

#### SDK Initialization
```typescript
declare global {
  interface Window {
    onSpotifyWebPlaybackSDKReady: () => void
    Spotify: typeof Spotify
  }
}

// Initialize player
const initializePlayer = (token: string): Promise<Spotify.Player> => {
  return new Promise((resolve) => {
    window.onSpotifyWebPlaybackSDKReady = () => {
      const player = new window.Spotify.Player({
        name: 'Spotify-Soulseek App',
        getOAuthToken: (cb) => cb(token),
        volume: 0.5
      })

      // Error handling
      player.addListener('initialization_error', ({ message }) => {
        console.error('Failed to initialize:', message)
      })

      player.addListener('authentication_error', ({ message }) => {
        console.error('Failed to authenticate:', message)
      })

      // Ready
      player.addListener('ready', ({ device_id }) => {
        console.log('Ready with Device ID', device_id)
        resolve(player)
      })

      player.connect()
    }
  })
}
```

#### Player Controls
```typescript
interface PlayerControls {
  play: (uri?: string) => Promise<void>
  pause: () => Promise<void>
  resume: () => Promise<void>
  seek: (position: number) => Promise<void>
  setVolume: (volume: number) => Promise<void>
  nextTrack: () => Promise<void>
  previousTrack: () => Promise<void>
}

// Play specific track
async function playTrack(player: Spotify.Player, trackUri: string): Promise<void> {
  const response = await fetch(`https://api.spotify.com/v1/me/player/play?device_id=${deviceId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      uris: [trackUri]
    })
  })
}
```

## Soulseek Protocol Implementation

### Connection Management

#### Server Connection
```python
# python/soulseek_client/protocol.py
import socket
import struct
import asyncio

class SoulseekProtocol:
    def __init__(self):
        self.server_socket = None
        self.server_host = "server.slsknet.org"
        self.server_port = 2242
    
    async def connect(self, username: str, password: str):
        """Connect to Soulseek server"""
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        await self.server_socket.connect((self.server_host, self.server_port))
        
        # Send login message
        login_msg = self._pack_login_message(username, password)
        await self.server_socket.send(login_msg)
        
        # Wait for response
        response = await self._receive_message()
        return self._parse_login_response(response)
```

#### Message Protocol
```python
# Message types
MESSAGE_TYPES = {
    'LOGIN': 1,
    'SET_WAIT_PORT': 2,
    'GET_PEER_ADDRESS': 3,
    'WATCH_USER': 5,
    'UNWATCH_USER': 6,
    'GET_USER_STATUS': 7,
    'SAY_CHATROOM': 13,
    'JOIN_ROOM': 14,
    'LEAVE_ROOM': 15,
    'USER_SEARCH': 26,
    'FILE_SEARCH': 42,
    'SET_STATUS': 28,
    'SERVER_PING': 50,
    'SEND_CONNECT_TOKEN': 52,
    'MOVE_DOWNLOAD': 103
}

def pack_message(msg_type: int, data: bytes) -> bytes:
    """Pack message with length header"""
    length = len(data) + 4
    return struct.pack('<I', length) + struct.pack('<I', msg_type) + data

def unpack_message(data: bytes) -> tuple[int, bytes]:
    """Unpack message and return type and data"""
    length = struct.unpack('<I', data[:4])[0]
    msg_type = struct.unpack('<I', data[4:8])[0]
    msg_data = data[8:length]
    return msg_type, msg_data
```

### Search Implementation

#### Search Request
```python
class SearchManager:
    def __init__(self, protocol: SoulseekProtocol):
        self.protocol = protocol
        self.active_searches = {}
        self.search_id_counter = 0
    
    async def search(self, query: str) -> int:
        """Initiate search and return search ID"""
        search_id = self.search_id_counter
        self.search_id_counter += 1
        
        # Pack search message
        search_data = self._pack_search_data(search_id, query)
        message = pack_message(MESSAGE_TYPES['FILE_SEARCH'], search_data)
        
        # Send search request
        await self.protocol.server_socket.send(message)
        
        # Store search info
        self.active_searches[search_id] = {
            'query': query,
            'results': [],
            'timestamp': time.time()
        }
        
        return search_id
    
    def _pack_search_data(self, search_id: int, query: str) -> bytes:
        """Pack search request data"""
        data = struct.pack('<I', search_id)
        data += struct.pack('<I', len(query))
        data += query.encode('utf-8')
        return data
```

#### Search Results
```python
@dataclass
class SearchResult:
    username: str
    filename: str
    filesize: int
    file_extension: str
    attributes: dict
    bitrate: int = 0
    duration: int = 0
    sample_rate: int = 0

class SearchResultParser:
    @staticmethod
    def parse_search_result(data: bytes) -> list[SearchResult]:
        """Parse search result message"""
        results = []
        offset = 0
        
        # Parse username
        username_len = struct.unpack('<I', data[offset:offset+4])[0]
        offset += 4
        username = data[offset:offset+username_len].decode('utf-8')
        offset += username_len
        
        # Parse number of results
        num_results = struct.unpack('<I', data[offset:offset+4])[0]
        offset += 4
        
        for _ in range(num_results):
            result = SearchResultParser._parse_single_result(data, offset, username)
            results.append(result)
            offset = result.next_offset
        
        return results
```

### Download Management

#### File Transfer Protocol
```python
class DownloadManager:
    def __init__(self):
        self.active_downloads = {}
        self.download_queue = []
    
    async def download_file(self, username: str, filename: str, filesize: int) -> str:
        """Download file from peer"""
        download_id = self._generate_download_id()
        
        # Connect to peer
        peer_socket = await self._connect_to_peer(username)
        
        # Request file
        request_msg = self._pack_download_request(filename)
        await peer_socket.send(request_msg)
        
        # Receive file data
        download_path = await self._receive_file_data(
            peer_socket, filename, filesize, download_id
        )
        
        return download_path
    
    async def _receive_file_data(self, socket, filename: str, filesize: int, download_id: str) -> str:
        """Receive and save file data"""
        download_path = os.path.join(DOWNLOAD_DIR, filename)
        bytes_received = 0
        
        with open(download_path, 'wb') as f:
            while bytes_received < filesize:
                chunk = await socket.recv(8192)
                if not chunk:
                    break
                
                f.write(chunk)
                bytes_received += len(chunk)
                
                # Update progress
                progress = (bytes_received / filesize) * 100
                await self._update_download_progress(download_id, progress)
        
        return download_path
```

## IPC Communication

### Electron IPC Handlers

#### Spotify Handlers
```typescript
// src/main/ipc/spotify-handlers.ts
import { ipcMain } from 'electron'
import { SpotifyService } from '../services/spotify-service'

export function registerSpotifyHandlers(spotifyService: SpotifyService): void {
  ipcMain.handle('spotify:authenticate', async () => {
    return await spotifyService.authenticate()
  })

  ipcMain.handle('spotify:getPlaylists', async () => {
    return await spotifyService.getPlaylists()
  })

  ipcMain.handle('spotify:getPlaylistTracks', async (_, playlistId: string) => {
    return await spotifyService.getPlaylistTracks(playlistId)
  })

  ipcMain.handle('spotify:playTrack', async (_, trackUri: string) => {
    return await spotifyService.playTrack(trackUri)
  })
}
```

#### Soulseek Handlers
```typescript
// src/main/ipc/soulseek-handlers.ts
export function registerSoulseekHandlers(soulseekService: SoulseekService): void {
  ipcMain.handle('soulseek:search', async (_, query: string) => {
    return await soulseekService.search(query)
  })

  ipcMain.handle('soulseek:download', async (_, username: string, filename: string) => {
    return await soulseekService.downloadFile(username, filename)
  })

  ipcMain.handle('soulseek:getDownloadProgress', async (_, downloadId: string) => {
    return await soulseekService.getDownloadProgress(downloadId)
  })
}
```

### Renderer IPC Calls
```typescript
// src/renderer/src/services/api.ts
export const spotifyApi = {
  authenticate: () => window.electron.ipcRenderer.invoke('spotify:authenticate'),
  getPlaylists: () => window.electron.ipcRenderer.invoke('spotify:getPlaylists'),
  getPlaylistTracks: (playlistId: string) => 
    window.electron.ipcRenderer.invoke('spotify:getPlaylistTracks', playlistId),
  playTrack: (trackUri: string) => 
    window.electron.ipcRenderer.invoke('spotify:playTrack', trackUri)
}

export const soulseekApi = {
  search: (query: string) => 
    window.electron.ipcRenderer.invoke('soulseek:search', query),
  download: (username: string, filename: string) => 
    window.electron.ipcRenderer.invoke('soulseek:download', username, filename),
  getDownloadProgress: (downloadId: string) => 
    window.electron.ipcRenderer.invoke('soulseek:getDownloadProgress', downloadId)
}
```

# Spotify-Soulseek Desktop App

A desktop application that integrates Spotify Web API with Soulseek P2P network functionality, allowing users to authenticate with Spotify, browse playlists, play track previews, and search/download tracks from the Soulseek network.

## Features

### Spotify Integration
- 🔐 OAuth 2.0 authentication with Spotify
- 📋 Browse and display user playlists
- 🎵 View tracks within playlists with metadata
- ▶️ Play track previews using Spotify Web Playback SDK
- 👤 User profile information display

### Soulseek Integration
- 🔍 Search for tracks on Soulseek P2P network
- 📊 Display search results with file information
- ⬇️ Download files from Soulseek peers
- 📈 Download progress tracking
- 📁 Download queue management

### User Interface
- 🎨 Modern design using Shadcn/ui components
- 🌙 Dark/light theme support
- 📱 Responsive layout
- ⚡ Fast and intuitive navigation
- 🎛️ Comprehensive settings panel

## Technology Stack

- **Frontend**: Electron + React + TypeScript
- **UI Framework**: Shadcn/ui + Tailwind CSS
- **Build Tool**: Vite + electron-vite
- **Backend Services**: Node.js + Python
- **Database**: SQLite for local storage
- **APIs**: Spotify Web API + Web Playback SDK
- **P2P Protocol**: Soulseek (based on Nicotine+ implementation)

## Project Structure

```
spotify-soulseek-app/
├── docs/                           # Project documentation
│   ├── PROJECT_OUTLINE.md          # Comprehensive project overview
│   ├── DEVELOPMENT_GUIDE.md        # Development setup and workflow
│   ├── API_DOCUMENTATION.md        # API integration details
│   ├── TECHNICAL_IMPLEMENTATION.md # Technical architecture guide
│   └── PROGRESS_CHECKLIST.md       # Development progress tracking
├── src/
│   ├── main/                       # Electron main process
│   ├── renderer/                   # React application
│   └── shared/                     # Shared types and utilities
├── python/                         # Python Soulseek implementation
└── package.json
```

## Quick Start

### Prerequisites
- Node.js (v18 or higher)
- Python (v3.8 or higher)
- Spotify Developer Account

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/odmustafa/spotify-api.git
   cd spotify-api
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Python environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r python/requirements.txt
   ```

4. **Configure Spotify API**
   - Create a Spotify app at [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
   - Create `.env` file with your credentials:
   ```env
   SPOTIFY_CLIENT_ID=your_client_id_here
   SPOTIFY_CLIENT_SECRET=your_client_secret_here
   SPOTIFY_REDIRECT_URI=http://localhost:3000/callback
   ```

5. **Start development**
   ```bash
   npm run dev
   ```

## Development

### Project Documentation
All development information is organized in the `/docs` folder:

- **[PROJECT_OUTLINE.md](docs/PROJECT_OUTLINE.md)** - Complete project overview and architecture
- **[DEVELOPMENT_GUIDE.md](docs/DEVELOPMENT_GUIDE.md)** - Setup instructions and development workflow
- **[API_DOCUMENTATION.md](docs/API_DOCUMENTATION.md)** - Spotify and Soulseek API integration details
- **[TECHNICAL_IMPLEMENTATION.md](docs/TECHNICAL_IMPLEMENTATION.md)** - Technical architecture and code examples
- **[PROGRESS_CHECKLIST.md](docs/PROGRESS_CHECKLIST.md)** - Development progress tracking

### Development Phases

1. **Phase 1**: Project Setup & Spotify Integration
2. **Phase 2**: Spotify Playback Implementation
3. **Phase 3**: Soulseek Integration Foundation
4. **Phase 4**: Soulseek Search & Download
5. **Phase 5**: Polish & Optimization

### Key Commands

```bash
# Development
npm run dev              # Start development environment
npm run build            # Build for production
npm run test             # Run tests

# Python service
source venv/bin/activate # Activate Python environment
python python/soulseek_client/server.py  # Start Soulseek service
```

## Architecture

The application uses a multi-process architecture:

- **Electron Main Process**: Handles system integration, file operations, and service management
- **Electron Renderer Process**: React-based UI with modern components
- **Python Service**: Soulseek protocol implementation and P2P operations
- **IPC Communication**: Seamless communication between all processes

## Important References

- [Spotify Web API Documentation](https://developer.spotify.com/documentation/web-api/)
- [Spotify Web Playback SDK](https://developer.spotify.com/documentation/web-playback-sdk/)
- [Nicotine+ Repository](https://github.com/nicotine-plus/nicotine-plus) (Soulseek reference)
- [Shadcn/ui Documentation](https://ui.shadcn.com/)

## Security & Compliance

- Secure token storage using electron-store
- Input validation and sanitization
- Compliance with Spotify Developer Terms
- Respect for Soulseek network protocols
- Safe file download handling

## Contributing

1. Read the development documentation in `/docs`
2. Follow the progress checklist for current development status
3. Ensure all tests pass before submitting changes
4. Follow the established code style and conventions

## License

This project is for educational and personal use. Please ensure compliance with:
- [Spotify Developer Terms of Service](https://developer.spotify.com/terms/)
- [Soulseek Network Rules](https://www.slsknet.org/news/node/681)

## Support

For development questions and issues:
1. Check the documentation in `/docs` folder
2. Review the progress checklist for current status
3. Refer to the technical implementation guide
4. Check API documentation for integration details

---

**Note**: This application requires a Spotify Premium subscription for full playback functionality. The Soulseek integration is based on the open-source Nicotine+ implementation and follows established P2P protocols.

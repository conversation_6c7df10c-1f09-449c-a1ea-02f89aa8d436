import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  
  // <PERSON><PERSON> expects a fixed port for development
  server: {
    port: 5173,
    strictPort: true,
  },

  // Build configuration for <PERSON><PERSON>
  build: {
    // <PERSON><PERSON> uses Chromium, so we can target modern browsers
    target: 'esnext',
    // Don't minify for better debugging
    minify: !process.env.TAURI_DEBUG ? 'esbuild' : false,
    // Produce sourcemaps for better debugging
    sourcemap: !!process.env.TAURI_DEBUG,
    // Output directory
    outDir: 'dist',
  },

  // Path resolution
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src/renderer/src'),
    },
  },

  // Root directory for the frontend
  root: 'src/renderer',

  // Environment variables
  envPrefix: ['VITE_', 'TAURI_'],

  // Clear screen on rebuild
  clearScreen: false,
})

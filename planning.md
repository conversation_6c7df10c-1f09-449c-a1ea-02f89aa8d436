Begin by initializing the git repository:
    *git remote add origin https://github.com/odmustafa/spotify-api.git*
    *git branch -M main*
    *git push -u origin main*

I need to create a desktop app that utilizes the Spotify WebAPI in order to allow me to: 
*1* Authenticate and sign-in to my Spotify account, 
*2* Display a list of my playlists,
*3* Select a specific playlist to display the songs within that playlist,
*4* Play a preview of each track within the app utilizing the Spotify Web Playback SDK [ please refer to the documentation found here: https://developer.spotify.com/documentation/web-playback-sdk/ ]

Use whatever language and framework you think would be best for this project. I only want to specify that Shadcn UI is utilized for the front end design for the app.

Also, look at this repo for a graphical client for the Soulseek peer-to-peer network: https://github.com/nicotine-plus/nicotine-plus

Analyze the code and figure out how to integrate a"Search Soulseek" button into our app, next to each track in the Spotify playlist mentioned earlier. The button should initiate a search for Artist Name - Track Name in the Soulseek network, and display the results. The file for each result should be downloadable, identical to how SoulSeek works. 

Begin by putting together a detailed project outline, and a very detailed guide on how to go about creating the app I described above. 

Create a folder containing these guides and outline files. Also I need a checklist to keep track of progress. Also, make it a rule to refer back to the files in this folder to keep focus on what features need to be created and added and all the parts that need to work together, as well as links to documentation and repos to refer to and abide by.


http://127.0.0.1:3000/callback
http://[::1]:3000/callback